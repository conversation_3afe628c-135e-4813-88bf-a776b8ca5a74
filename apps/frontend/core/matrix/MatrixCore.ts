/**
 * 矩阵核心引擎
 * 🎯 核心价值：统一的矩阵处理引擎，数据驱动渲染，业务模式切换
 * 📦 功能范围：数据处理管道、渲染引擎、交互处理、性能优化
 * 🔄 架构设计：基于配置驱动的模块化架构，支持插件式业务模式
 */

import type {
  BusinessMode,
  CellData,
  CellRenderData,
  Coordinate,
  InteractionEvent,
  MatrixConfig,
  MatrixData,
  ModeHandler,
  PerformanceMetrics,
  ProcessedMatrixData,
} from './MatrixTypes';

import {
  DEFAULT_COLOR_VALUES,
  getCachedGroupAData,
  getMatrixDataByCoordinate
} from '../data/GroupAData';

import { coordinateKey } from './MatrixTypes';

// ===== 业务模式处理器 =====

/** 坐标模式处理器 */
const coordinateModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: `${cell.x},${cell.y}`,
        style: {
          backgroundColor: cell.isSelected ? '#e3f2fd' : '#ffffff',
          border: '1px solid #e0e0e0',
          color: '#333333',
          fontSize: '12px',
        },
        className: `matrix-cell coordinate-mode ${cell.isSelected ? 'selected' : ''}`,
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'coordinate',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: `${cell.x},${cell.y}`,
    style: {
      backgroundColor: cell.isSelected ? '#e3f2fd' : '#ffffff',
      border: '1px solid #e0e0e0',
      color: '#333333',
      fontSize: '12px',
    },
    className: `matrix-cell coordinate-mode ${cell.isSelected ? 'selected' : ''}`,
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Coordinate mode interaction: ${event.type} at (${cell.x}, ${cell.y})`);
  },
};

/** 颜色模式处理器 */
const colorModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      // 检查是否有矩阵数据
      const groupAData = getCachedGroupAData();
      const matrixData = getMatrixDataByCoordinate(groupAData, cell.x, cell.y);
      const cellColor = matrixData?.color || cell.color;

      // 从DEFAULT_COLOR_VALUES获取16进制颜色值
      const backgroundColor = cellColor ? DEFAULT_COLOR_VALUES[cellColor].hex : '#f5f5f5';

      // 黑色格子显示组别字符，其他颜色显示颜色名称
      let content = '';
      if (matrixData) {
        if (matrixData.color === 'black') {
          content = matrixData.group; // 黑色显示组别字符
        } else {
          content = matrixData.color; // 其他颜色显示颜色名称
        }
      }

      renderData.set(key, {
        content,
        style: {
          backgroundColor,
          border: cell.isSelected ? '2px solid #000000' : '1px solid #e0e0e0',
          color: cellColor ? '#ffffff' : '#666666',
          fontSize: '10px',
          fontWeight: 'bold',
        },
        className: `matrix-cell color-mode ${cellColor ? `color-${cellColor}` : ''} ${cell.isSelected ? 'selected' : ''} ${matrixData ? 'has-matrix-data' : ''}`,
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'color',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => {
    // 从DEFAULT_COLOR_VALUES获取16进制颜色值
    const backgroundColor = cell.color ? DEFAULT_COLOR_VALUES[cell.color].hex : '#f5f5f5';

    return {
      content: cell.color || '',
      style: {
        backgroundColor,
        border: cell.isSelected ? '2px solid #000000' : '1px solid #e0e0e0',
        color: cell.color ? '#ffffff' : '#666666',
        fontSize: '10px',
        fontWeight: 'bold',
      },
      className: `matrix-cell color-mode ${cell.color ? `color-${cell.color}` : ''} ${cell.isSelected ? 'selected' : ''}`,
      isInteractive: true,
    };
  },

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Color mode interaction: ${event.type} at (${cell.x}, ${cell.y}), color: ${cell.color}`);
  },
};

/** 等级模式处理器 */
const levelModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: cell.level?.toString() || '',
        style: {
          backgroundColor: cell.isSelected ? '#fff3cd' : '#ffffff',
          border: '1px solid #e0e0e0',
          color: cell.level ? '#333333' : '#999999',
          fontSize: '14px',
          fontWeight: cell.level ? 'bold' : 'normal',
        },
        className: `matrix-cell level-mode ${cell.level ? 'has-level' : ''} ${cell.isSelected ? 'selected' : ''}`,
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'level',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: cell.level?.toString() || '',
    style: {
      backgroundColor: cell.isSelected ? '#fff3cd' : '#ffffff',
      border: '1px solid #e0e0e0',
      color: cell.level ? '#333333' : '#999999',
      fontSize: '14px',
      fontWeight: cell.level ? 'bold' : 'normal',
    },
    className: `matrix-cell level-mode ${cell.level ? 'has-level' : ''} ${cell.isSelected ? 'selected' : ''}`,
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Level mode interaction: ${event.type} at (${cell.x}, ${cell.y}), level: ${cell.level}`);
  },
};

/** 词语模式处理器 */
const wordModeHandler: ModeHandler = {
  processData: (data: MatrixData, config: MatrixConfig): ProcessedMatrixData => {
    const renderData = new Map<string, CellRenderData>();

    data.cells.forEach((cell, key) => {
      renderData.set(key, {
        content: cell.word || '',
        style: {
          backgroundColor: cell.isSelected ? '#e8f5e8' : '#ffffff',
          border: '1px solid #e0e0e0',
          color: cell.word ? '#333333' : '#cccccc',
          fontSize: '11px',
          fontWeight: 'normal',
        },
        className: `matrix-cell word-mode ${cell.word ? 'has-word' : ''} ${cell.isSelected ? 'selected' : ''}`,
        isInteractive: true,
      });
    });

    return {
      cells: data.cells,
      renderData,
      metadata: {
        totalCells: data.cells.size,
        activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
        selectedCells: data.selectedCells.size,
        mode: 'word',
      },
    };
  },

  renderCell: (cell: CellData, config: MatrixConfig): CellRenderData => ({
    content: cell.word || '',
    style: {
      backgroundColor: cell.isSelected ? '#e8f5e8' : '#ffffff',
      border: '1px solid #e0e0e0',
      color: cell.word ? '#333333' : '#cccccc',
      fontSize: '11px',
      fontWeight: 'normal',
    },
    className: `matrix-cell word-mode ${cell.word ? 'has-word' : ''} ${cell.isSelected ? 'selected' : ''}`,
    isInteractive: true,
  }),

  handleInteraction: (event: InteractionEvent, cell: CellData) => {
    console.log(`Word mode interaction: ${event.type} at (${cell.x}, ${cell.y}), word: ${cell.word}`);
  },
};

// ===== 模式处理器注册表 =====

const modeHandlers: Record<BusinessMode, ModeHandler> = {
  coordinate: coordinateModeHandler,
  color: colorModeHandler,
  level: levelModeHandler,
  word: wordModeHandler,
};

// ===== 矩阵核心引擎 =====

export class MatrixCore {
  private performanceMetrics: PerformanceMetrics = {
    renderTime: 0,
    updateTime: 0,
    cacheHitRate: 0,
    memoryUsage: 0,
    frameRate: 60,
  };

  /**
   * 处理矩阵数据
   */
  processData(data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const startTime = performance.now();

    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    const result = handler.processData(data, config);

    const endTime = performance.now();
    this.performanceMetrics.renderTime = endTime - startTime;

    return result;
  }

  /**
   * 渲染单个单元格
   */
  renderCell(cell: CellData, config: MatrixConfig): CellRenderData {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.renderCell(cell, config);
  }

  /**
   * 处理交互事件
   */
  handleInteraction(event: InteractionEvent, cell: CellData, config: MatrixConfig): void {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    handler.handleInteraction(event, cell);
  }

  /**
   * 切换业务模式
   */
  switchMode(mode: BusinessMode, data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const newConfig = { ...config, mode };
    return this.processData(data, newConfig);
  }

  /**
   * 批量更新单元格
   */
  batchUpdateCells(
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>,
    data: MatrixData
  ): MatrixData {
    const startTime = performance.now();

    const newData = { ...data };
    const newCells = new Map(data.cells);

    updates.forEach(({ coordinate, data: cellData }) => {
      const key = coordinateKey(coordinate.x, coordinate.y);
      const existingCell = newCells.get(key);

      if (existingCell) {
        newCells.set(key, { ...existingCell, ...cellData });
      }
    });

    newData.cells = newCells;

    const endTime = performance.now();
    this.performanceMetrics.updateTime = endTime - startTime;

    return newData;
  }

  /**
   * 获取性能指标
   */
  getPerformanceMetrics(): PerformanceMetrics {
    return { ...this.performanceMetrics };
  }

  /**
   * 重置性能指标
   */
  resetPerformanceMetrics(): void {
    this.performanceMetrics = {
      renderTime: 0,
      updateTime: 0,
      cacheHitRate: 0,
      memoryUsage: 0,
      frameRate: 60,
    };
  }

  /**
   * 验证矩阵数据
   */
  validateData(data: MatrixData): boolean {
    // 检查数据完整性
    if (!data.cells || !(data.cells instanceof Map)) {
      return false;
    }

    // 检查单元格数据
    for (const [key, cell] of data.cells) {
      if (!this.validateCell(cell)) {
        console.warn(`Invalid cell data at ${key}:`, cell);
        return false;
      }
    }

    return true;
  }

  /**
   * 验证单元格数据
   */
  private validateCell(cell: CellData): boolean {
    return (
      typeof cell.x === 'number' &&
      typeof cell.y === 'number' &&
      cell.x >= 0 && cell.x < 33 &&
      cell.y >= 0 && cell.y < 33 &&
      typeof cell.isActive === 'boolean' &&
      typeof cell.isSelected === 'boolean' &&
      typeof cell.isHovered === 'boolean'
    );
  }
}

// ===== 单例实例 =====

export const matrixCore = new MatrixCore();

// ===== 工具函数 =====

/**
 * 创建交互事件
 */
export const createInteractionEvent = (
  type: InteractionEvent['type'],
  coordinate: Coordinate,
  modifiers: Partial<InteractionEvent['modifiers']> = {},
  data?: any
): InteractionEvent => ({
  type,
  coordinate,
  modifiers: {
    ctrl: false,
    shift: false,
    alt: false,
    ...modifiers,
  },
  data,
});

/**
 * 注册自定义模式处理器
 */
export const registerModeHandler = (mode: string, handler: ModeHandler): void => {
  (modeHandlers as any)[mode] = handler;
};

/**
 * 获取可用的业务模式
 */
export const getAvailableModes = (): BusinessMode[] => {
  return Object.keys(modeHandlers) as BusinessMode[];
};
