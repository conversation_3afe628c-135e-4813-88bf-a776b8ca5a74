/**
 * 矩阵样式工厂
 * 🎯 核心价值：消除重复样式逻辑，统一样式生成，提高维护性
 * 📦 功能范围：样式常量、样式工厂函数、类名生成器
 * 🔄 架构设计：工厂模式，支持不同模式的样式生成
 */

import type { CellData, CellStyle, BusinessMode, BasicColorType } from './MatrixTypes';
import { DEFAULT_COLOR_VALUES } from '../data/GroupAData';

// ===== 样式常量 =====

/** 基础样式常量 */
export const STYLE_CONSTANTS = {
  // 边框样式
  BORDER: {
    DEFAULT: '1px solid #e0e0e0',
    SELECTED: '2px solid #000000',
    SELECTED_COORDINATE: '2px solid #3b82f6',
  },
  
  // 字体大小
  FONT_SIZE: {
    COORDINATE: '12px',
    COLOR: '10px',
    LEVEL: '14px',
    WORD: '11px',
  },
  
  // 背景色
  BACKGROUND: {
    DEFAULT: '#ffffff',
    FALLBACK: '#f5f5f5',
    SELECTED: {
      COORDINATE: '#e3f2fd',
      COLOR: '#000000',
      LEVEL: '#fff3cd',
      WORD: '#e8f5e8',
    },
  },
  
  // 文字颜色
  TEXT_COLOR: {
    DEFAULT: '#333333',
    MUTED: '#666666',
    LIGHT_MUTED: '#999999',
    VERY_LIGHT: '#cccccc',
    WHITE: '#ffffff',
  },
  
  // 字体粗细
  FONT_WEIGHT: {
    NORMAL: 'normal',
    BOLD: 'bold',
  },
} as const;

// ===== 颜色处理工具 =====

/**
 * 获取单元格的背景颜色
 */
export const getCellBackgroundColor = (
  cell: CellData, 
  mode: BusinessMode,
  matrixDataColor?: BasicColorType
): string => {
  const cellColor = matrixDataColor || cell.color;
  
  if (cell.isSelected) {
    return STYLE_CONSTANTS.BACKGROUND.SELECTED[mode.toUpperCase() as keyof typeof STYLE_CONSTANTS.BACKGROUND.SELECTED] || STYLE_CONSTANTS.BACKGROUND.DEFAULT;
  }
  
  if (mode === 'color' && cellColor) {
    return DEFAULT_COLOR_VALUES[cellColor].hex;
  }
  
  return STYLE_CONSTANTS.BACKGROUND.DEFAULT;
};

/**
 * 获取单元格的文字颜色
 */
export const getCellTextColor = (cell: CellData, mode: BusinessMode): string => {
  switch (mode) {
    case 'color':
      return cell.color ? STYLE_CONSTANTS.TEXT_COLOR.WHITE : STYLE_CONSTANTS.TEXT_COLOR.MUTED;
    case 'level':
      return cell.level ? STYLE_CONSTANTS.TEXT_COLOR.DEFAULT : STYLE_CONSTANTS.TEXT_COLOR.LIGHT_MUTED;
    case 'word':
      return cell.word ? STYLE_CONSTANTS.TEXT_COLOR.DEFAULT : STYLE_CONSTANTS.TEXT_COLOR.VERY_LIGHT;
    default:
      return STYLE_CONSTANTS.TEXT_COLOR.DEFAULT;
  }
};

/**
 * 获取单元格的边框样式
 */
export const getCellBorder = (cell: CellData, mode: BusinessMode): string => {
  if (cell.isSelected) {
    return mode === 'coordinate' 
      ? STYLE_CONSTANTS.BORDER.SELECTED_COORDINATE 
      : STYLE_CONSTANTS.BORDER.SELECTED;
  }
  return STYLE_CONSTANTS.BORDER.DEFAULT;
};

// ===== 样式工厂函数 =====

/**
 * 创建单元格样式
 */
export const createCellStyle = (
  cell: CellData, 
  mode: BusinessMode,
  matrixDataColor?: BasicColorType
): CellStyle => {
  const style: CellStyle = {
    backgroundColor: getCellBackgroundColor(cell, mode, matrixDataColor),
    border: getCellBorder(cell, mode),
    color: getCellTextColor(cell, mode),
    fontSize: STYLE_CONSTANTS.FONT_SIZE[mode.toUpperCase() as keyof typeof STYLE_CONSTANTS.FONT_SIZE],
  };

  // 模式特定的样式
  switch (mode) {
    case 'color':
      style.fontWeight = STYLE_CONSTANTS.FONT_WEIGHT.BOLD;
      break;
    case 'level':
      style.fontWeight = cell.level ? STYLE_CONSTANTS.FONT_WEIGHT.BOLD : STYLE_CONSTANTS.FONT_WEIGHT.NORMAL;
      break;
    case 'word':
      style.fontWeight = STYLE_CONSTANTS.FONT_WEIGHT.NORMAL;
      break;
    default:
      // coordinate mode 默认不设置 fontWeight
      break;
  }

  return style;
};

// ===== 类名生成器 =====

/**
 * 创建单元格类名
 */
export const createCellClassName = (
  cell: CellData, 
  mode: BusinessMode,
  matrixDataColor?: BasicColorType,
  hasMatrixData?: boolean
): string => {
  const classes = ['matrix-cell', `${mode}-mode`];
  
  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');
  if (cell.isActive) classes.push('active');
  
  // 模式特定的类名
  const cellColor = matrixDataColor || cell.color;
  switch (mode) {
    case 'color':
      if (cellColor) classes.push(`color-${cellColor}`);
      if (hasMatrixData) classes.push('has-matrix-data');
      break;
    case 'level':
      if (cell.level) classes.push('has-level');
      break;
    case 'word':
      if (cell.word) classes.push('has-word');
      break;
  }
  
  return classes.join(' ');
};

// ===== 内容生成器 =====

/**
 * 创建单元格内容
 */
export const createCellContent = (cell: CellData, mode: BusinessMode): string => {
  switch (mode) {
    case 'coordinate':
      return `${cell.x},${cell.y}`;
    case 'color':
      return ''; // 颜色模式不显示文字
    case 'level':
      return cell.level?.toString() || '';
    case 'word':
      return cell.word || '';
    default:
      return '';
  }
};
