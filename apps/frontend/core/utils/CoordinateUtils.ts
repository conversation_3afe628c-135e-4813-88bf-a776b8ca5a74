/**
 * 坐标工具模块
 * 🎯 核心价值：统一坐标处理逻辑，消除重复代码，提供一致的坐标转换API
 * 📦 功能范围：坐标转换、坐标验证、坐标键生成、坐标解析
 * 🔄 架构设计：纯函数设计，无副作用，高性能
 */

import type { Coordinate } from '../matrix/MatrixTypes';
import { MATRIX_SIZE } from '../matrix/MatrixTypes';

// ===== 坐标常量 =====

/** 网格中心点坐标 */
export const GRID_CENTER = {
  X: 16,
  Y: 16,
} as const;

/** 坐标范围 */
export const COORDINATE_BOUNDS = {
  MIN: 0,
  MAX: MATRIX_SIZE - 1,
  CENTER: GRID_CENTER,
  DISPLAY_MIN: -16,
  DISPLAY_MAX: 16,
} as const;

// ===== 坐标键处理 =====

/**
 * 生成坐标键
 * @param x X坐标
 * @param y Y坐标
 * @returns 坐标键字符串
 */
export const coordinateKey = (x: number, y: number): string => `${x},${y}`;

/**
 * 解析坐标键
 * @param key 坐标键字符串
 * @returns 坐标对象
 */
export const parseCoordinateKey = (key: string): Coordinate => {
  const [x, y] = key.split(',').map(Number);
  return { x, y };
};

// ===== 坐标转换 =====

/**
 * 将相对坐标转换为绝对网格坐标
 * @param relativeX 相对X坐标
 * @param relativeY 相对Y坐标
 * @returns 绝对坐标元组 [x, y]
 */
export const toAbsoluteCoordinate = (relativeX: number, relativeY: number): [number, number] => {
  return [GRID_CENTER.X + relativeX, GRID_CENTER.Y + relativeY];
};

/**
 * 将网格坐标转换为以(16,16)为中心点(0,0)的显示坐标
 * @param gridX 网格X坐标 (0-32)
 * @param gridY 网格Y坐标 (0-32)
 * @returns 显示坐标元组 [displayX, displayY]，范围(-16到16)
 */
export const toDisplayCoordinate = (gridX: number, gridY: number): [number, number] => {
  return [gridX - GRID_CENTER.X, gridY - GRID_CENTER.Y];
};

/**
 * 将显示坐标转换为网格坐标
 * @param displayX 显示X坐标 (-16到16)
 * @param displayY 显示Y坐标 (-16到16)
 * @returns 网格坐标元组 [gridX, gridY]，范围(0-32)
 */
export const fromDisplayCoordinate = (displayX: number, displayY: number): [number, number] => {
  return [displayX + GRID_CENTER.X, displayY + GRID_CENTER.Y];
};

// ===== 坐标验证 =====

/**
 * 验证网格坐标是否有效
 * @param x X坐标
 * @param y Y坐标
 * @returns 是否有效
 */
export const isValidGridCoordinate = (x: number, y: number): boolean => {
  return (
    Number.isInteger(x) &&
    Number.isInteger(y) &&
    x >= COORDINATE_BOUNDS.MIN &&
    x <= COORDINATE_BOUNDS.MAX &&
    y >= COORDINATE_BOUNDS.MIN &&
    y <= COORDINATE_BOUNDS.MAX
  );
};

/**
 * 验证显示坐标是否有效
 * @param x 显示X坐标
 * @param y 显示Y坐标
 * @returns 是否有效
 */
export const isValidDisplayCoordinate = (x: number, y: number): boolean => {
  return (
    Number.isInteger(x) &&
    Number.isInteger(y) &&
    x >= COORDINATE_BOUNDS.DISPLAY_MIN &&
    x <= COORDINATE_BOUNDS.DISPLAY_MAX &&
    y >= COORDINATE_BOUNDS.DISPLAY_MIN &&
    y <= COORDINATE_BOUNDS.DISPLAY_MAX
  );
};

/**
 * 验证坐标对象是否有效
 * @param coordinate 坐标对象
 * @returns 是否有效
 */
export const isValidCoordinate = (coordinate: Coordinate): boolean => {
  return isValidGridCoordinate(coordinate.x, coordinate.y);
};

// ===== 坐标计算 =====

/**
 * 计算两个坐标之间的距离
 * @param coord1 坐标1
 * @param coord2 坐标2
 * @returns 欧几里得距离
 */
export const calculateDistance = (coord1: Coordinate, coord2: Coordinate): number => {
  const dx = coord1.x - coord2.x;
  const dy = coord1.y - coord2.y;
  return Math.sqrt(dx * dx + dy * dy);
};

/**
 * 计算两个坐标之间的曼哈顿距离
 * @param coord1 坐标1
 * @param coord2 坐标2
 * @returns 曼哈顿距离
 */
export const calculateManhattanDistance = (coord1: Coordinate, coord2: Coordinate): number => {
  return Math.abs(coord1.x - coord2.x) + Math.abs(coord1.y - coord2.y);
};

/**
 * 应用偏移到坐标
 * @param coordinate 原始坐标
 * @param offsetX X偏移
 * @param offsetY Y偏移
 * @returns 新坐标
 */
export const applyOffset = (coordinate: Coordinate, offsetX: number, offsetY: number): Coordinate => {
  return {
    x: coordinate.x + offsetX,
    y: coordinate.y + offsetY,
  };
};

/**
 * 限制坐标在有效范围内
 * @param coordinate 坐标
 * @returns 限制后的坐标
 */
export const clampCoordinate = (coordinate: Coordinate): Coordinate => {
  return {
    x: Math.max(COORDINATE_BOUNDS.MIN, Math.min(COORDINATE_BOUNDS.MAX, coordinate.x)),
    y: Math.max(COORDINATE_BOUNDS.MIN, Math.min(COORDINATE_BOUNDS.MAX, coordinate.y)),
  };
};

// ===== 坐标格式化 =====

/**
 * 格式化网格坐标为字符串
 * @param coordinate 坐标
 * @returns 格式化字符串
 */
export const formatGridCoordinate = (coordinate: Coordinate): string => {
  return `(${coordinate.x},${coordinate.y})`;
};

/**
 * 格式化显示坐标为字符串
 * @param coordinate 坐标
 * @returns 格式化字符串
 */
export const formatDisplayCoordinate = (coordinate: Coordinate): string => {
  const [displayX, displayY] = toDisplayCoordinate(coordinate.x, coordinate.y);
  return `(${displayX},${displayY})`;
};

// ===== 坐标信息 =====

/**
 * 获取坐标的详细信息
 * @param coordinate 坐标
 * @returns 坐标信息对象
 */
export const getCoordinateInfo = (coordinate: Coordinate) => {
  const [displayX, displayY] = toDisplayCoordinate(coordinate.x, coordinate.y);
  
  return {
    grid: {
      x: coordinate.x,
      y: coordinate.y,
      formatted: formatGridCoordinate(coordinate),
    },
    display: {
      x: displayX,
      y: displayY,
      formatted: `(${displayX},${displayY})`,
    },
    isCenter: displayX === 0 && displayY === 0,
    isValid: isValidCoordinate(coordinate),
    key: coordinateKey(coordinate.x, coordinate.y),
  };
};
