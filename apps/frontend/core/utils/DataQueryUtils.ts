/**
 * 数据查询工具模块
 * 🎯 核心价值：统一数据查询逻辑，消除重复查询函数，提供高性能查询API
 * 📦 功能范围：通用查询、条件过滤、数据聚合、性能优化
 * 🔄 架构设计：泛型设计，支持多种查询条件，内置缓存优化
 */

import type { 
  MatrixDataSet, 
  MatrixDataPoint, 
  BasicColorType, 
  DataLevel, 
  GroupType 
} from '../matrix/MatrixTypes';

// ===== 查询条件类型 =====

/** 查询条件 */
export interface QueryCondition<T = any> {
  field: keyof MatrixDataPoint;
  value: T;
  operator?: 'equals' | 'in' | 'range' | 'exists';
}

/** 复合查询条件 */
export interface CompoundQuery {
  conditions: QueryCondition[];
  operator: 'and' | 'or';
}

/** 查询选项 */
export interface QueryOptions {
  limit?: number;
  offset?: number;
  sortBy?: keyof MatrixDataPoint;
  sortOrder?: 'asc' | 'desc';
  useCache?: boolean;
}

// ===== 查询结果类型 =====

/** 查询结果 */
export interface QueryResult<T = MatrixDataPoint> {
  data: T[];
  total: number;
  hasMore: boolean;
  executionTime: number;
  fromCache: boolean;
}

// ===== 缓存管理 =====

/** 查询缓存 */
const queryCache = new Map<string, { result: QueryResult; timestamp: number }>();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存

/**
 * 生成查询缓存键
 */
const generateCacheKey = (condition: QueryCondition | CompoundQuery, options?: QueryOptions): string => {
  return JSON.stringify({ condition, options });
};

/**
 * 清理过期缓存
 */
const cleanExpiredCache = (): void => {
  const now = Date.now();
  for (const [key, { timestamp }] of queryCache.entries()) {
    if (now - timestamp > CACHE_TTL) {
      queryCache.delete(key);
    }
  }
};

// ===== 核心查询函数 =====

/**
 * 通用数据查询函数
 * @param dataSet 数据集
 * @param condition 查询条件
 * @param options 查询选项
 * @returns 查询结果
 */
export const queryData = (
  dataSet: MatrixDataSet,
  condition: QueryCondition | CompoundQuery,
  options: QueryOptions = {}
): QueryResult => {
  const startTime = performance.now();
  const cacheKey = generateCacheKey(condition, options);
  
  // 检查缓存
  if (options.useCache !== false) {
    const cached = queryCache.get(cacheKey);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
      return {
        ...cached.result,
        executionTime: performance.now() - startTime,
        fromCache: true,
      };
    }
  }
  
  // 执行查询
  let results: MatrixDataPoint[];
  
  if ('conditions' in condition) {
    // 复合查询
    results = executeCompoundQuery(dataSet, condition);
  } else {
    // 单条件查询
    results = executeSingleQuery(dataSet, condition);
  }
  
  // 排序
  if (options.sortBy) {
    results = sortResults(results, options.sortBy, options.sortOrder);
  }
  
  // 分页
  const total = results.length;
  const offset = options.offset || 0;
  const limit = options.limit;
  
  if (limit !== undefined) {
    results = results.slice(offset, offset + limit);
  }
  
  const result: QueryResult = {
    data: results,
    total,
    hasMore: limit !== undefined && offset + limit < total,
    executionTime: performance.now() - startTime,
    fromCache: false,
  };
  
  // 缓存结果
  if (options.useCache !== false) {
    queryCache.set(cacheKey, { result, timestamp: Date.now() });
    cleanExpiredCache();
  }
  
  return result;
};

/**
 * 执行单条件查询
 */
const executeSingleQuery = (dataSet: MatrixDataSet, condition: QueryCondition): MatrixDataPoint[] => {
  const { field, value, operator = 'equals' } = condition;
  
  // 优化：使用预建索引
  if (field === 'color' && operator === 'equals') {
    return dataSet.byColor.get(value as BasicColorType) || [];
  }
  
  if (field === 'level' && operator === 'equals') {
    return dataSet.byLevel.get(value as DataLevel) || [];
  }
  
  if (field === 'group' && operator === 'equals') {
    return dataSet.byGroup.get(value as GroupType) || [];
  }
  
  // 通用查询
  return dataSet.points.filter(point => {
    const fieldValue = point[field];
    
    switch (operator) {
      case 'equals':
        return fieldValue === value;
      case 'in':
        return Array.isArray(value) && value.includes(fieldValue);
      case 'exists':
        return fieldValue !== undefined && fieldValue !== null;
      case 'range':
        if (Array.isArray(value) && value.length === 2) {
          const [min, max] = value;
          return fieldValue >= min && fieldValue <= max;
        }
        return false;
      default:
        return false;
    }
  });
};

/**
 * 执行复合查询
 */
const executeCompoundQuery = (dataSet: MatrixDataSet, query: CompoundQuery): MatrixDataPoint[] => {
  const { conditions, operator } = query;
  
  if (conditions.length === 0) return [];
  
  if (operator === 'and') {
    return dataSet.points.filter(point =>
      conditions.every(condition => {
        const singleResult = executeSingleQuery(dataSet, condition);
        return singleResult.some(p => p.x === point.x && p.y === point.y);
      })
    );
  } else {
    // 'or' operator
    const resultSet = new Set<MatrixDataPoint>();
    conditions.forEach(condition => {
      const singleResult = executeSingleQuery(dataSet, condition);
      singleResult.forEach(point => resultSet.add(point));
    });
    return Array.from(resultSet);
  }
};

/**
 * 排序结果
 */
const sortResults = (
  results: MatrixDataPoint[],
  sortBy: keyof MatrixDataPoint,
  sortOrder: 'asc' | 'desc' = 'asc'
): MatrixDataPoint[] => {
  return results.sort((a, b) => {
    const aValue = a[sortBy];
    const bValue = b[sortBy];
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  });
};

// ===== 便捷查询函数 =====

/**
 * 按颜色查询
 */
export const queryByColor = (dataSet: MatrixDataSet, color: BasicColorType): MatrixDataPoint[] => {
  return queryData(dataSet, { field: 'color', value: color }).data;
};

/**
 * 按级别查询
 */
export const queryByLevel = (dataSet: MatrixDataSet, level: DataLevel): MatrixDataPoint[] => {
  return queryData(dataSet, { field: 'level', value: level }).data;
};

/**
 * 按组查询
 */
export const queryByGroup = (dataSet: MatrixDataSet, group: GroupType): MatrixDataPoint[] => {
  return queryData(dataSet, { field: 'group', value: group }).data;
};

/**
 * 按坐标查询
 */
export const queryByCoordinate = (dataSet: MatrixDataSet, x: number, y: number): MatrixDataPoint | undefined => {
  const results = queryData(dataSet, {
    conditions: [
      { field: 'x', value: x },
      { field: 'y', value: y }
    ],
    operator: 'and'
  }).data;
  
  return results[0];
};

/**
 * 按多个颜色查询
 */
export const queryByColors = (dataSet: MatrixDataSet, colors: BasicColorType[]): MatrixDataPoint[] => {
  return queryData(dataSet, { field: 'color', value: colors, operator: 'in' }).data;
};

/**
 * 按级别范围查询
 */
export const queryByLevelRange = (dataSet: MatrixDataSet, minLevel: DataLevel, maxLevel: DataLevel): MatrixDataPoint[] => {
  return queryData(dataSet, { field: 'level', value: [minLevel, maxLevel], operator: 'range' }).data;
};

// ===== 统计查询 =====

/**
 * 获取数据统计信息
 */
export const getDataStatistics = (dataSet: MatrixDataSet) => {
  return {
    totalPoints: dataSet.points.length,
    colorCounts: Object.fromEntries(dataSet.byColor.entries().map(([color, points]) => [color, points.length])),
    levelCounts: Object.fromEntries(dataSet.byLevel.entries().map(([level, points]) => [level, points.length])),
    groupCounts: Object.fromEntries(dataSet.byGroup.entries().map(([group, points]) => [group, points.length])),
  };
};

// ===== 缓存管理 =====

/**
 * 清空查询缓存
 */
export const clearQueryCache = (): void => {
  queryCache.clear();
};

/**
 * 获取缓存统计信息
 */
export const getCacheStats = () => {
  return {
    size: queryCache.size,
    keys: Array.from(queryCache.keys()),
  };
};
